'use client';

import { useEffect, useState } from 'react';
import { TranslationLayout } from '@/components/layout/TranslationLayout';
import { ProjectList } from '@/components/project/ProjectList';
import { useAppContext } from '@/store/AppContext';
import { projectsAPI } from '@/lib/api-client';
import { ProjectResponse, ProjectPageResponse } from '@/types/api';
import { LoadingOverlay } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';
import { SettingsPanel } from '@/components/settings/SettingsPanel';

export default function Home() {
  const { state, dispatch } = useAppContext();
  const { currentProject, currentPage, loading, errors } = state;
  const [selectedProjectId, setSelectedProjectId] = useState<string | undefined>(undefined);
  const [showSettings, setShowSettings] = useState(false);

  // Load projects on mount
  useEffect(() => {
    const loadProjects = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'projects', loading: true } });
        dispatch({ type: 'SET_ERROR', payload: { key: 'projects', error: null } });

        const response = await projectsAPI.getProjects();
        dispatch({ type: 'SET_PROJECTS', payload: response.items });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load projects';
        dispatch({ type: 'SET_ERROR', payload: { key: 'projects', error: errorMessage } });
        console.error('Failed to load projects:', err);
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'projects', loading: false } });
      }
    };

    loadProjects();
  }, [dispatch, currentProject]);

  // Handle project selection
  const handleProjectSelect = async (project: ProjectResponse) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'currentProject', loading: true } });
      dispatch({ type: 'SET_ERROR', payload: { key: 'currentProject', error: null } });
      setSelectedProjectId(project.id);
      dispatch({ type: 'SET_CURRENT_PROJECT', payload: project });

      // Load project pages
      const pagesResponse = await projectsAPI.getProjectPages(project.id);
      if (pagesResponse.items.length > 0) {
        // Select first page
        dispatch({ type: 'SET_CURRENT_PAGE', payload: pagesResponse.items[0] });
      } else {
        dispatch({ type: 'SET_CURRENT_PAGE', payload: null });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load project';
      dispatch({ type: 'SET_ERROR', payload: { key: 'currentProject', error: errorMessage } });
      console.error('Failed to load project:', err);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'currentProject', loading: false } });
    }
  };

  // Handle page selection
  const handlePageSelect = async (pageId: string) => {
    if (!currentProject) return;

    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'currentPage', loading: true } });
      dispatch({ type: 'SET_ERROR', payload: { key: 'currentPage', error: null } });

      const page = await projectsAPI.getProjectPage(currentProject.id, pageId);
      dispatch({ type: 'SET_CURRENT_PAGE', payload: page });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load page';
      dispatch({ type: 'SET_ERROR', payload: { key: 'currentPage', error: errorMessage } });
      console.error('Failed to load page:', err);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'currentPage', loading: false } });
    }
  };

  // Generate image URL for current page
  const getImageUrl = (page: ProjectPageResponse | null): string | undefined => {
    if (!page || !page.file_path) return undefined;

    // If file_path is already a full URL, use it directly
    if (page.file_path.startsWith('http')) {
      return page.file_path;
    }

    // Otherwise, construct URL relative to backend
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
    return `${baseUrl}${page.file_path}`;
  };

  // Show project selection if no project is selected
  if (!currentProject) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center relative">
        {/* Loading overlay for initial project loading */}
        <LoadingOverlay
          isVisible={loading.projects}
          message="Loading projects..."
          className="z-50"
        />

        <div className="max-w-4xl w-full mx-4">
          <div className="text-center mb-8 relative">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">ho-trans</h1>
            <p className="text-gray-600">Manga Translation Tool</p>

            {/* Settings Button */}
            <button
              onClick={() => setShowSettings(true)}
              className="absolute top-0 right-0 p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              title="Settings"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>

          {!loading.projects && (
            <ProjectList
              onProjectSelect={handleProjectSelect}
              selectedProjectId={selectedProjectId}
              className="max-w-2xl mx-auto"
            />
          )}

          {errors.projects && !loading.projects && (
            <div className="mt-4 max-w-2xl mx-auto">
              <ErrorDisplay
                title="Failed to load projects"
                message={errors.projects}
                severity="error"
                onRetry={() => {
                  // Retry loading projects
                  dispatch({ type: 'SET_ERROR', payload: { key: 'projects', error: null } });
                  window.location.reload();
                }}
                onDismiss={() => {
                  dispatch({ type: 'SET_ERROR', payload: { key: 'projects', error: null } });
                }}
              />
            </div>
          )}
        </div>
      </div>
    );
  }

  // Show main translation interface
  return (
    <div className="h-screen relative">
      {/* Loading overlay for project/page changes */}
      <LoadingOverlay
        isVisible={loading.currentProject || loading.currentPage}
        message={loading.currentProject ? "Loading project..." : "Loading page..."}
        className="z-50"
      />

      <TranslationLayout
        currentProject={currentProject}
        currentPage={currentPage}
        imageUrl={getImageUrl(currentPage)}
        onProjectChange={handleProjectSelect}
        onPageChange={handlePageSelect}
      />

      {/* Error displays for project/page loading */}
      {errors.currentProject && (
        <div className="absolute top-4 right-4 z-40 max-w-md">
          <ErrorDisplay
            title="Project Error"
            message={errors.currentProject}
            severity="error"
            onRetry={() => {
              dispatch({ type: 'SET_ERROR', payload: { key: 'currentProject', error: null } });
              if (currentProject) {
                handleProjectSelect(currentProject);
              }
            }}
            onDismiss={() => {
              dispatch({ type: 'SET_ERROR', payload: { key: 'currentProject', error: null } });
            }}
          />
        </div>
      )}

      {errors.currentPage && (
        <div className="absolute top-4 right-4 z-40 max-w-md">
          <ErrorDisplay
            title="Page Error"
            message={errors.currentPage}
            severity="error"
            onRetry={() => {
              dispatch({ type: 'SET_ERROR', payload: { key: 'currentPage', error: null } });
              // Retry loading current page
              if (currentProject && currentPage) {
                handlePageSelect(currentPage.id);
              }
            }}
            onDismiss={() => {
              dispatch({ type: 'SET_ERROR', payload: { key: 'currentPage', error: null } });
            }}
          />
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <SettingsPanel
              onClose={() => setShowSettings(false)}
              className="w-full h-full"
            />
          </div>
        </div>
      )}
    </div>
  );
}
