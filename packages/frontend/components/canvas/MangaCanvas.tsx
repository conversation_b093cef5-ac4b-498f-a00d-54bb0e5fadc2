'use client';

import React, { useRef, useEffect, useCallback, useState } from 'react';
import * as fabric from 'fabric';
import {
  CanvasState,
  CanvasTool,
  CanvasImageInfo,
  CanvasEventHandlers,
  CanvasConfig,
  DEFAULT_CANVAS_CONFIG
} from '@/types/canvas';

export interface MangaCanvasRef {
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;
  setTool: (tool: CanvasTool) => void;
  getCanvas: () => fabric.Canvas | null;
  getImageInfo: () => CanvasImageInfo | null;
  getCanvasState: () => CanvasState;
}

interface MangaCanvasProps {
  imageUrl?: string;
  width?: number;
  height?: number;
  config?: Partial<CanvasConfig>;
  onCanvasReady?: (canvas: fabric.Canvas) => void;
  onStateChange?: (state: CanvasState) => void;
  eventHandlers?: Partial<CanvasEventHandlers>;
  className?: string;
}

export const MangaCanvas = React.forwardRef<MangaCanvasRef, MangaCanvasProps>(({
  imageUrl,
  width = 800,
  height = 600,
  config = {},
  onCanvasReady,
  onStateChange,
  eventHandlers = {},
  className = ''
}, ref) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null);
  const imageRef = useRef<fabric.FabricImage | null>(null);

  const [canvasState, setCanvasState] = useState<CanvasState>({
    zoom: 1,
    panX: 0,
    panY: 0,
    isDrawing: false,
    selectedTool: CanvasTool.SELECT
  });

  const [imageInfo, setImageInfo] = useState<CanvasImageInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const canvasConfig = { ...DEFAULT_CANVAS_CONFIG, ...config };

  // Update canvas state and notify parent
  const updateCanvasState = useCallback((updates: Partial<CanvasState>) => {
    setCanvasState(prev => {
      const newState = { ...prev, ...updates };
      onStateChange?.(newState);
      return newState;
    });
  }, [onStateChange]);

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasRef.current) return;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width,
      height,
      backgroundColor: '#f8f9fa',
      selection: true,
      preserveObjectStacking: true,
      imageSmoothingEnabled: true,
      enableRetinaScaling: true
    });

    fabricCanvasRef.current = canvas;

    // Set up canvas event listeners
    canvas.on('mouse:wheel', handleMouseWheel);
    canvas.on('mouse:down', handleMouseDown);
    canvas.on('mouse:move', handleMouseMove);
    canvas.on('mouse:up', handleMouseUp);
    canvas.on('selection:created', handleSelectionCreated);
    canvas.on('selection:cleared', handleSelectionCleared);
    canvas.on('object:modified', handleObjectModified);

    // Notify parent that canvas is ready
    onCanvasReady?.(canvas);

    return () => {
      canvas.dispose();
      fabricCanvasRef.current = null;
    };
  }, [width, height]);

  // Load image when imageUrl changes
  useEffect(() => {
    if (!imageUrl || !fabricCanvasRef.current) return;

    loadImage(imageUrl);
  }, [imageUrl]);

  // Load and display image on canvas
  const loadImage = useCallback(async (url: string) => {
    if (!fabricCanvasRef.current) return;

    setIsLoading(true);
    setError(null);

    try {
      const img = await fabric.FabricImage.fromURL(url);

      const canvas = fabricCanvasRef.current;

      // Remove existing image if any
      if (imageRef.current) {
        canvas.remove(imageRef.current);
      }

      // Calculate scaling to fit canvas while maintaining aspect ratio
      const canvasWidth = canvas.getWidth();
      const canvasHeight = canvas.getHeight();
      const imgWidth = img.width || 1;
      const imgHeight = img.height || 1;

      const scaleX = canvasWidth / imgWidth;
      const scaleY = canvasHeight / imgHeight;
      const scale = Math.min(scaleX, scaleY) * 0.9; // 90% of available space

      img.set({
        scaleX: scale,
        scaleY: scale,
        left: (canvasWidth - imgWidth * scale) / 2,
        top: (canvasHeight - imgHeight * scale) / 2,
        selectable: false,
        evented: false,
        hoverCursor: 'default',
        moveCursor: 'default'
      });

      // Add image to canvas as background
      canvas.add(img);
      canvas.sendObjectToBack(img);
      imageRef.current = img;

      // Update image info
      setImageInfo({
        originalWidth: imgWidth,
        originalHeight: imgHeight,
        displayWidth: imgWidth * scale,
        displayHeight: imgHeight * scale,
        scaleX: scale,
        scaleY: scale,
        imageUrl: url
      });

      canvas.renderAll();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load image');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Handle mouse wheel for zooming
  const handleMouseWheel = useCallback((opt: any) => {
    const canvas = fabricCanvasRef.current;
    if (!canvas || !opt.e) return;

    const delta = opt.e.deltaY;
    let zoom = canvas.getZoom();
    zoom *= 0.999 ** delta;

    // Clamp zoom level
    zoom = Math.max(canvasConfig.minZoom, Math.min(canvasConfig.maxZoom, zoom));

    // Zoom towards mouse position
    const point = new fabric.Point(opt.e.offsetX, opt.e.offsetY);
    canvas.zoomToPoint(point, zoom);

    updateCanvasState({ zoom });
    opt.e.preventDefault();
    opt.e.stopPropagation();
  }, [canvasConfig.minZoom, canvasConfig.maxZoom, updateCanvasState]);

  // Handle mouse down for panning and drawing
  const handleMouseDown = useCallback((opt: any) => {
    const canvas = fabricCanvasRef.current;
    if (!canvas || !opt.e) return;

    if (canvasState.selectedTool === CanvasTool.PAN) {
      (canvas as any).isDragging = true;
      canvas.selection = false;
      (canvas as any).lastPosX = opt.e.clientX;
      (canvas as any).lastPosY = opt.e.clientY;
      updateCanvasState({ isDrawing: true });
    }
  }, [canvasState.selectedTool, updateCanvasState]);

  // Handle mouse move for panning
  const handleMouseMove = useCallback((opt: any) => {
    const canvas = fabricCanvasRef.current;
    if (!canvas || !opt.e) return;

    if ((canvas as any).isDragging && canvasState.selectedTool === CanvasTool.PAN) {
      const vpt = canvas.viewportTransform;
      if (vpt) {
        vpt[4] += opt.e.clientX - ((canvas as any).lastPosX || 0);
        vpt[5] += opt.e.clientY - ((canvas as any).lastPosY || 0);
        canvas.requestRenderAll();
        (canvas as any).lastPosX = opt.e.clientX;
        (canvas as any).lastPosY = opt.e.clientY;

        updateCanvasState({
          panX: vpt[4],
          panY: vpt[5]
        });
      }
    }
  }, [canvasState.selectedTool, updateCanvasState]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    (canvas as any).isDragging = false;
    canvas.selection = canvasState.selectedTool === CanvasTool.SELECT;
    updateCanvasState({ isDrawing: false });
  }, [canvasState.selectedTool, updateCanvasState]);

  // Handle object selection
  const handleSelectionCreated = useCallback((opt: any) => {
    const activeObject = opt.selected?.[0];
    if (activeObject && (activeObject as any).data?.regionId) {
      eventHandlers.onRegionSelect?.((activeObject as any).data.regionId);
    }
  }, [eventHandlers]);

  // Handle selection cleared
  const handleSelectionCleared = useCallback(() => {
    updateCanvasState({ selectedRegion: undefined });
  }, [updateCanvasState]);

  // Handle object modification
  const handleObjectModified = useCallback((opt: any) => {
    const activeObject = opt.target;
    if (activeObject && (activeObject as any).data?.regionId) {
      // Convert canvas coordinates back to normalized coordinates
      const region = convertCanvasToNormalized(activeObject);
      eventHandlers.onRegionUpdate?.((activeObject as any).data.regionId, region);
    }
  }, [eventHandlers]);

  // Convert canvas coordinates to normalized coordinates (0-1)
  const convertCanvasToNormalized = useCallback((obj: fabric.Object) => {
    if (!imageInfo) return {};

    const left = obj.left || 0;
    const top = obj.top || 0;
    const width = (obj.width || 0) * (obj.scaleX || 1);
    const height = (obj.height || 0) * (obj.scaleY || 1);

    return {
      x: left / imageInfo.displayWidth,
      y: top / imageInfo.displayHeight,
      width: width / imageInfo.displayWidth,
      height: height / imageInfo.displayHeight
    };
  }, [imageInfo]);

  // Public methods for external control
  const zoomIn = useCallback(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    const zoom = Math.min(canvasConfig.maxZoom, canvas.getZoom() + canvasConfig.zoomStep);
    canvas.setZoom(zoom);
    updateCanvasState({ zoom });
  }, [canvasConfig.maxZoom, canvasConfig.zoomStep, updateCanvasState]);

  const zoomOut = useCallback(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    const zoom = Math.max(canvasConfig.minZoom, canvas.getZoom() - canvasConfig.zoomStep);
    canvas.setZoom(zoom);
    updateCanvasState({ zoom });
  }, [canvasConfig.minZoom, canvasConfig.zoomStep, updateCanvasState]);

  const resetZoom = useCallback(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    canvas.setZoom(1);
    canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);
    updateCanvasState({ zoom: 1, panX: 0, panY: 0 });
  }, [updateCanvasState]);

  const setTool = useCallback((tool: CanvasTool) => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    canvas.selection = tool === CanvasTool.SELECT;
    canvas.isDrawingMode = false;

    updateCanvasState({ selectedTool: tool });
  }, [updateCanvasState]);

  // Expose methods to parent component via ref
  React.useImperativeHandle(ref, () => ({
    zoomIn,
    zoomOut,
    resetZoom,
    setTool,
    getCanvas: () => fabricCanvasRef.current,
    getImageInfo: () => imageInfo,
    getCanvasState: () => canvasState
  }), [zoomIn, zoomOut, resetZoom, setTool, imageInfo, canvasState]);

  // Notify parent when canvas is ready
  useEffect(() => {
    if (onCanvasReady && fabricCanvasRef.current) {
      onCanvasReady(fabricCanvasRef.current);
    }
  }, [onCanvasReady]);

  return (
    <div className={`relative ${className}`}>
      <canvas
        ref={canvasRef}
        className="border border-gray-300 rounded-lg shadow-sm"
      />

      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className="text-sm text-gray-600">Loading image...</span>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-red-50 bg-opacity-90 rounded-lg">
          <div className="text-center">
            <div className="text-red-600 text-sm font-medium">Failed to load image</div>
            <div className="text-red-500 text-xs mt-1">{error}</div>
          </div>
        </div>
      )}
    </div>
  );
});

MangaCanvas.displayName = 'MangaCanvas';

export default MangaCanvas;
