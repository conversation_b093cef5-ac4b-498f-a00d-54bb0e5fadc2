'use client';

import React, { useState, useEffect } from 'react';
import { ProjectPageResponse, PaginatedResponse } from '@/types/api';
import { projectsAPI } from '@/lib/api-client';
import { buildImageUrl, buildThumbnailUrl } from '@/lib/api-utils';

interface ProjectPagesProps {
  projectId: string;
  onPageSelect: (page: ProjectPageResponse) => void;
  selectedPageId?: string;
  className?: string;
}

export const ProjectPages: React.FC<ProjectPagesProps> = ({
  projectId,
  onPageSelect,
  selectedPageId,
  className = ''
}) => {
  const [pages, setPages] = useState<ProjectPageResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });

  // Load pages
  const loadPages = async (page: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      const response: PaginatedResponse<ProjectPageResponse> = await projectsAPI.getProjectPages(
        projectId,
        { page, limit: pagination.limit }
      );

      // Safely handle response.items - ensure it's always an array
      setPages(response && response.items ? response.items : []);
      setPagination({
        page: response?.meta?.page || 1,
        limit: response?.meta?.limit || pagination.limit,
        total: response?.meta?.total || 0,
        pages: response?.meta?.pages || 0
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load pages');
      // Reset pages to empty array on error
      setPages([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load pages on mount and when projectId changes
  useEffect(() => {
    if (projectId) {
      loadPages();
    }
  }, [projectId]);

  // Handle page deletion
  const handleDeletePage = async (pageId: string) => {
    if (!confirm('Are you sure you want to delete this page?')) return;

    try {
      await projectsAPI.deleteProjectPage(projectId, pageId);
      setPages(prev => prev ? prev.filter(page => page.id !== pageId) : []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete page');
    }
  };

  // Handle pagination
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.pages) {
      loadPages(newPage);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'processing':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading pages...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Project Pages ({pagination.total})
          </h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md ${viewMode === 'grid'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-400 hover:text-gray-600'
                }`}
              title="Grid view"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md ${viewMode === 'list'
                ? 'bg-blue-100 text-blue-700'
                : 'text-gray-400 hover:text-gray-600'
                }`}
              title="List view"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {error && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={() => loadPages(pagination.page)}
              className="mt-1 text-xs text-red-700 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        )}
      </div>

      {/* Pages Content */}
      <div className="p-4">
        {!pages || pages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">No pages uploaded yet</p>
            <p className="text-xs text-gray-400 mt-1">Upload some manga pages to get started</p>
          </div>
        ) : viewMode === 'grid' ? (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {pages && pages.map((page) => (
              <div
                key={page.id}
                onClick={() => onPageSelect(page)}
                className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${selectedPageId === page.id
                  ? 'border-blue-500 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
                  }`}
              >
                {/* Thumbnail */}
                <div className="aspect-[3/4] bg-gray-100">
                  <img
                    src={buildThumbnailUrl(page.file_path, 200)}
                    alt={`Page ${page.page_number}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjI2NyIgdmlld0JveD0iMCAwIDIwMCAyNjciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjY3IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NyA5OEw4NyAxMDJMMTEzIDEwMkwxMTMgOThIODdaIiBmaWxsPSIjOUNBM0FGIi8+CjxwYXRoIGQ9Ik04NyAxMTBMODcgMTM4TDExMyAxMzhMMTEzIDExMEg4N1oiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+';
                    }}
                  />
                </div>

                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all">
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeletePage(page.id);
                      }}
                      className="p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                      title="Delete page"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Info */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                  <div className="text-white text-xs">
                    <div className="font-medium">Page {page.page_number}</div>
                    <div className="flex items-center justify-between">
                      <span>{page.text_region_count} regions</span>
                      <span className={`px-1.5 py-0.5 rounded text-xs ${getStatusColor(page.ocr_status)}`}>
                        {page.ocr_status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="space-y-2">
            {pages && pages.map((page) => (
              <div
                key={page.id}
                onClick={() => onPageSelect(page)}
                className={`flex items-center space-x-4 p-3 rounded-lg cursor-pointer transition-colors ${selectedPageId === page.id
                  ? 'bg-blue-50 border border-blue-200'
                  : 'hover:bg-gray-50 border border-transparent'
                  }`}
              >
                {/* Thumbnail */}
                <div className="flex-shrink-0">
                  <img
                    src={buildThumbnailUrl(page.file_path, 60)}
                    alt={`Page ${page.page_number}`}
                    className="w-12 h-16 object-cover rounded border border-gray-200"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDgiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA0OCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQ4IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyNEwyMCAyNkwyOCAyNkwyOCAyNEgyMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTIwIDMwTDIwIDM0TDI4IDM0TDI4IDMwSDIwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                    }}
                  />
                </div>

                {/* Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      Page {page.page_number}
                    </h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(page.ocr_status)}`}>
                      {page.ocr_status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 truncate">{page.original_filename}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                    <span>{formatFileSize(page.file_size)}</span>
                    {page.image_width && page.image_height && (
                      <span>{page.image_width} × {page.image_height}</span>
                    )}
                    <span>{page.text_region_count} regions</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex-shrink-0">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeletePage(page.id);
                    }}
                    className="p-1 text-gray-400 hover:text-red-600 rounded"
                    title="Delete page"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="p-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
              {pagination.total} pages
            </p>
            <div className="flex space-x-1">
              <button
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={pagination.page <= 1}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <button
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={pagination.page >= pagination.pages}
                className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectPages;
